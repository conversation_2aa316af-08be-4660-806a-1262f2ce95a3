import 'package:flutter/material.dart';
import 'package:ilyas/screens/splash_screen.dart';
import 'core/profile_manager.dart';
import 'core/settings_manager.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await ProfileManager.instance.initialize();
  } catch (e, stackTrace) {
    debugPrint('Error initializing ProfileManager: $e');
    debugPrint('$stackTrace');
  }

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // Listen to settings changes to rebuild the app with new theme
    SettingsManager.instance.addListener(_onSettingsChanged);
  }

  @override
  void dispose() {
    SettingsManager.instance.removeListener(_onSettingsChanged);
    super.dispose();
  }

  void _onSettingsChanged() {
    setState(() {
      // Rebuild the app when settings change
    });
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'TaskTime',
      theme: SettingsManager.instance.lightTheme,
      darkTheme: SettingsManager.instance.darkTheme,
      themeMode:
          SettingsManager.instance.isDarkMode
              ? ThemeMode.dark
              : ThemeMode.light,
      home: const WelcomeScreen(),
    );
  }
}
