import 'package:flutter/material.dart';
import 'package:ilyas/screens/splash_screen.dart';
import 'core/profile_manager.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await ProfileManager.instance.initialize();
  } catch (e, stackTrace) {
    debugPrint('Error initializing ProfileManager: $e');
    debugPrint('$stackTrace');
  }

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: const WelcomeScreen(),
    );
  }
}
