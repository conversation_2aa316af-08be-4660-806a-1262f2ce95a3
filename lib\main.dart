import 'package:flutter/material.dart';
import 'screens/splash_screen.dart';
import 'core/profile_manager.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await ProfileManager.instance.initialize();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData.light(),
      darkTheme: ThemeData.dark(),
      themeMode:
          ThemeMode.system, // أو ThemeMode.dark لو تريد دائماً الوضع الليلي
      home: const WelcomeScreen(),
    );
  }
}

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Center(
        child: Text(
          'النص هنا',
          style: TextStyle(
            color: Colors.black,
          ),
        ),
      ),
    );
  }
}
