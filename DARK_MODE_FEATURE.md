# ميزة الوضع الليلي (Dark Mode) - دليل المستخدم

## 🌙 نظرة عامة
تم تفعيل وتحسين ميزة الوضع الليلي في تطبيق TaskTime بشكل كامل. يمكن للمستخدمين الآن التبديل بسهولة بين الوضع الليلي والوضع النهاري مع حفظ الإعدادات بشكل دائم.

## ✨ الميزات المُحدثة

### 1. حفظ الإعدادات بشكل دائم
- ✅ تم إضافة `SharedPreferences` لحفظ إعدادات الوضع الليلي
- ✅ الإعدادات تُحفظ تلقائياً عند التغيير
- ✅ الإعدادات تُستعاد عند إعادة تشغيل التطبيق

### 2. تطبيق الثيم على كامل التطبيق
- ✅ تم تحديث `main.dart` لاستخدام `SettingsManager`
- ✅ الثيم يتغير فورياً في جميع الشاشات
- ✅ دعم كامل للـ `ThemeMode.dark` و `ThemeMode.light`

### 3. واجهة مستخدم محسنة
- ✅ بطاقة مخصصة للوضع الليلي في شاشة الإعدادات
- ✅ تصميم متدرج جميل يتغير حسب الوضع الحالي
- ✅ أيقونات ديناميكية تتغير حسب الحالة

## 🎯 طرق الوصول لميزة الوضع الليلي

### 1. من شاشة الإعدادات الرئيسية
```
الإعدادات → بطاقة الوضع الليلي (في الأعلى)
```

### 2. من لوحة الإعدادات السريعة
```
الإعدادات → Quick Settings Panel → Dark Mode Toggle
```

### 3. من شاشة Dashboard
```
Dashboard → أيقونة الوضع الليلي (في الأعلى)
```

## 🎨 التصميم والألوان

### الوضع الليلي (Dark Mode)
- **خلفية رئيسية**: `#0D1117` (أسود داكن)
- **خلفية البطاقات**: `#161B22` (رمادي داكن)
- **النصوص**: `#E6EDF3` (أبيض مائل للرمادي)
- **الألوان الأساسية**: `#667EEA` و `#764BA2`

### الوضع النهاري (Light Mode)
- **خلفية رئيسية**: `#F8F9FA` (أبيض مائل للرمادي)
- **خلفية البطاقات**: `#FFFFFF` (أبيض نقي)
- **النصوص**: `#2D3748` (أسود مائل للرمادي)
- **الألوان الأساسية**: `#667EEA` و `#764BA2`

## 🔧 التحديثات التقنية

### 1. ملف `lib/main.dart`
```dart
// تم إضافة:
- import 'core/settings_manager.dart'
- تهيئة SettingsManager في main()
- تطبيق الثيم الديناميكي
- مراقبة تغييرات الإعدادات
```

### 2. ملف `lib/core/settings_manager.dart`
```dart
// تم إضافة:
- SharedPreferences للحفظ الدائم
- وظائف initialize() و _saveSettings()
- حفظ تلقائي عند تغيير الإعدادات
```

### 3. ملف `lib/screens/settings_screen.dart`
```dart
// تم إضافة:
- بطاقة مخصصة للوضع الليلي
- تصميم متدرج ديناميكي
- أيقونات متحركة
```

## 📱 تجربة المستخدم

### التبديل السريع
1. اضغط على بطاقة الوضع الليلي في الإعدادات
2. سيتغير الثيم فوراً في جميع الشاشات
3. ستظهر رسالة تأكيد بنجاح التغيير
4. الإعداد سيُحفظ تلقائياً

### المؤشرات البصرية
- **أيقونة القمر** 🌙: للوضع الليلي
- **أيقونة الشمس** ☀️: للوضع النهاري
- **مفتاح التبديل**: يظهر الحالة الحالية
- **الألوان المتدرجة**: تتغير حسب الوضع

## 🚀 الاستخدام

### للمطورين
```dart
// للحصول على حالة الوضع الليلي
bool isDark = SettingsManager.instance.isDarkMode;

// لتغيير الوضع
SettingsManager.instance.setDarkMode(true);

// للتبديل
SettingsManager.instance.toggleDarkMode();
```

### للمستخدمين
1. افتح التطبيق
2. اذهب إلى الإعدادات
3. اضغط على بطاقة الوضع الليلي
4. استمتع بالتجربة الجديدة!

## 🎉 النتيجة
تم تفعيل ميزة الوضع الليلي بنجاح مع:
- ✅ حفظ دائم للإعدادات
- ✅ تطبيق فوري على كامل التطبيق
- ✅ واجهة مستخدم جميلة ومتجاوبة
- ✅ تجربة مستخدم سلسة ومريحة

الآن يمكن للمستخدمين الاستمتاع بتجربة مريحة للعينين في الإضاءة المنخفضة! 🌙✨
